'use client';

import {
  ArrowLeft,
  CheckCircle,
  Globe,
  Package,
  Shield,
  User,
  XCircle,
  Loader2,
  Mail,
  FileText,
  Hash,
  Clock,
  Plus,
  Server,
  ExternalLink,
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast, Toaster } from 'sonner';

import { MainHeader } from '@/components/main/layout/main-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { formatDuration } from '@/lib/utils';
import { useOrderStore } from '@/store/order/action';
import { useProjectStore } from '@/store/project/action';

// Using native date formatting to match existing codebase patterns

export default function OrderDetailPage() {
  const { selectedOrder, orderLoading, fetchOrder } = useOrderStore();
  const { createProjectFromOrder, creatingFromOrder } = useProjectStore();
  const router = useRouter();
  const params = useParams();
  const orderId = params.id as string;
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (orderId) {
      fetchOrder(parseInt(orderId));
    }
  }, [orderId, fetchOrder]);

  const handleBack = () => {
    router.push('/orders');
  };

  const handleCreateProject = async () => {
    if (!selectedOrder) {
      return;
    }

    try {
      const response = await createProjectFromOrder({
        order_id: selectedOrder.id,
      });

      if (response?.status) {
        toast.success('Project created successfully');
        // Redirect to project detail page
        router.push(`/projects/${response.data.id}`);
      } else {
        toast.error(response?.message || 'Failed to create project');
      }
    } catch (error) {
      console.error('Failed to create project:', error);
      toast.error('Failed to create project');
    }
  };

  // Don't render interactive elements until client-side hydration is complete
  if (!isClient) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={handleBack}
              className='flex items-center gap-2'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-medium tracking-tight'>
                Loading Order...
              </h1>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (orderLoading) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={handleBack}
              className='flex items-center gap-2'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-medium tracking-tight'>
                Loading Order...
              </h1>
            </div>
          </div>
          <div className='flex items-center justify-center py-12'>
            <Loader2 className='h-8 w-8 animate-spin text-muted-foreground' />
          </div>
        </div>
      </div>
    );
  }

  if (!selectedOrder) {
    return (
      <div className='flex flex-1 flex-col'>
        <MainHeader />
        <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={handleBack}
              className='flex items-center gap-2'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-medium tracking-tight'>
                Order Not Found
              </h1>
            </div>
          </div>
          <div className='flex flex-col items-center justify-center py-12'>
            <Package className='h-12 w-12 text-muted-foreground mb-4' />
            <h3 className='text-lg font-semibold'>Order not found</h3>
            <p className='text-muted-foreground mb-4'>
              The order you are looking for doesn not exist or has been removed.
            </p>
            <Button onClick={handleBack}>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back to Orders
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-1 flex-col'>
      <MainHeader />
      <Toaster />

      <div className='flex flex-1 flex-col gap-6 p-4 pt-0'>
        {/* Header Section */}
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleBack}
            className='flex items-center gap-2'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <div className='flex-1'>
            <div className='flex items-center gap-3 mb-2'>
              <h1 className='text-3xl font-medium tracking-tight'>
                {selectedOrder.name}
              </h1>
              <Badge
                variant={selectedOrder.is_confirmed ? 'default' : 'secondary'}
                className='flex items-center gap-1'
              >
                {selectedOrder.is_confirmed ? (
                  <CheckCircle className='h-3 w-3' />
                ) : (
                  <XCircle className='h-3 w-3' />
                )}
                {selectedOrder.is_confirmed ? 'Confirmed' : 'Pending'}
              </Badge>
            </div>
            <p className='text-muted-foreground'>
              Order #{selectedOrder.code} • Created{' '}
              {new Date(selectedOrder.created_at).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </p>
          </div>
          {/*{selectedOrder.is_confirmed && (*/}
          <Button
            onClick={handleCreateProject}
            disabled={creatingFromOrder}
            className='flex items-center gap-2'
          >
            {creatingFromOrder ? (
              <Loader2 className='h-4 w-4 animate-spin' />
            ) : (
              <Plus className='h-4 w-4' />
            )}
            {creatingFromOrder ? 'Creating...' : 'Create Project'}
          </Button>
          {/*)}*/}
        </div>

        <div className='grid gap-6 md:grid-cols-2'>
          {/* Order Information */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Package className='h-5 w-5' />
                Order Information
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid gap-3'>
                <div className='flex items-center gap-3'>
                  <Hash className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <p className='text-sm font-medium'>Line Code</p>
                    <p className='text-sm text-muted-foreground font-mono'>
                      {selectedOrder.line_code || 'Not specified'}
                    </p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <Hash className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <p className='text-sm font-medium'>Order Code</p>
                    <p className='text-sm text-muted-foreground'>
                      {selectedOrder.code}
                    </p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <FileText className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <p className='text-sm font-medium'>Description</p>
                    <p className='text-sm text-muted-foreground'>
                      {selectedOrder.description || 'No description provided'}
                    </p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <div>
                    <p className='text-sm font-medium'>Template</p>
                    <Badge variant='outline'>
                      {selectedOrder.template.name}
                    </Badge>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <div>
                    <p className='text-sm font-medium'>Duration</p>
                    <p className='text-sm text-muted-foreground'>
                      {formatDuration(selectedOrder.duration)}
                    </p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <Clock className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <p className='text-sm font-medium'>Last Updated</p>
                    <p className='text-sm text-muted-foreground'>
                      {new Date(selectedOrder.updated_at).toLocaleDateString(
                        'en-US',
                        {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                        }
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* User Information */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <User className='h-5 w-5' />
                User Information
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid gap-3'>
                <div className='flex items-center gap-3'>
                  <User className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <p className='text-sm font-medium'>Name</p>
                    <p className='text-sm text-muted-foreground'>
                      {selectedOrder.user.name}
                    </p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <Mail className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <p className='text-sm font-medium'>Email</p>
                    <p className='text-sm text-muted-foreground'>
                      {selectedOrder.user.email}
                    </p>
                  </div>
                </div>

                <div className='flex items-center gap-3'>
                  <Shield className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <p className='text-sm font-medium'>User Type</p>
                    <Badge variant='outline'>
                      {selectedOrder.user.user_type.name}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Order Domains */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Globe className='h-5 w-5' />
              Order Domains ({selectedOrder.order_domains?.length || 0})
            </CardTitle>
            <CardDescription>
              Domains associated with this order
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!selectedOrder.order_domains ||
            selectedOrder.order_domains.length === 0 ? (
              <div className='flex flex-col items-center justify-center py-8 text-center'>
                <Globe className='h-12 w-12 text-muted-foreground mb-4' />
                <h3 className='text-lg font-semibold'>No domains</h3>
                <p className='text-muted-foreground'>
                  {`This order doesn't have any domains associated with it.`}
                </p>
              </div>
            ) : (
              <div className='grid gap-4'>
                {selectedOrder.order_domains.map(domain => (
                  <div
                    key={domain.id}
                    className='p-4 border rounded-lg space-y-3'
                  >
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-3'>
                        <Globe className='h-4 w-4 text-muted-foreground' />
                        <div>
                          <p className='font-medium font-mono'>{domain.name}</p>
                          {domain.created_at && (
                            <p className='text-xs text-muted-foreground'>
                              Added{' '}
                              {new Date(domain.created_at).toLocaleDateString(
                                'en-US',
                                {
                                  year: 'numeric',
                                  month: 'short',
                                  day: 'numeric',
                                }
                              )}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className='flex items-center gap-2'>
                        <Badge
                          variant={
                            domain.is_available ? 'default' : 'secondary'
                          }
                          className='text-xs flex items-center gap-1'
                        >
                          {domain.is_available && (
                            <Shield className='h-3 w-3' />
                          )}
                          {domain.is_available ? 'Available' : 'Unavailable'}
                        </Badge>
                      </div>
                    </div>

                    {/* Price Information */}
                    {domain.price !== undefined && domain.price !== null && (
                      <div className='space-y-2'>
                        <h5 className='text-sm font-medium text-muted-foreground'>
                          Price
                        </h5>
                        <div className='text-sm'>
                          <span className='font-medium font-mono'>
                            ฿{domain.price.toFixed(2)} THB
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Nameserver Information */}
                    {(domain.nameserver_1 || domain.nameserver_2) && (
                      <div className='space-y-2'>
                        <h5 className='text-sm font-medium text-muted-foreground'>
                          Nameservers
                        </h5>
                        <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                          {domain.nameserver_1 && (
                            <div className='text-sm'>
                              <span className='font-medium'>NS1:</span>{' '}
                              <span className='font-mono'>
                                {domain.nameserver_1}
                              </span>
                            </div>
                          )}
                          {domain.nameserver_2 && (
                            <div className='text-sm'>
                              <span className='font-medium'>NS2:</span>{' '}
                              <span className='font-mono'>
                                {domain.nameserver_2}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Domain Type Flags */}
                    {(domain.is_internal || domain.is_external) && (
                      <div className='space-y-2'>
                        <h5 className='text-sm font-medium text-muted-foreground'>
                          Domain Type
                        </h5>
                        <div className='flex gap-2'>
                          {domain.is_internal && (
                            <Badge variant='outline' className='text-xs'>
                              Internal
                            </Badge>
                          )}
                          {domain.is_external && (
                            <Badge variant='outline' className='text-xs'>
                              External
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Projects */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Server className='h-5 w-5' />
              Projects ({selectedOrder.projects?.length || 0})
            </CardTitle>
            <CardDescription>Projects created from this order</CardDescription>
          </CardHeader>
          <CardContent>
            {!selectedOrder.projects || selectedOrder.projects.length === 0 ? (
              <div className='flex flex-col items-center justify-center py-8 text-center'>
                <Server className='h-12 w-12 text-muted-foreground mb-4' />
                <h3 className='text-lg font-semibold'>No projects</h3>
                <p className='text-muted-foreground'>
                  {`This order doesn't have any projects created yet.`}
                </p>
              </div>
            ) : (
              <div className='grid gap-4'>
                {selectedOrder.projects.map(project => (
                  <div
                    key={project.id}
                    className='border rounded-lg p-4 space-y-3'
                  >
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-3'>
                        <Server className='h-4 w-4 text-muted-foreground' />
                        <div>
                          <p className='font-medium'>{project.name}</p>
                          <p className='text-sm text-muted-foreground'>
                            {project.slug}
                          </p>
                        </div>
                      </div>
                      <div className='flex items-center gap-2'>
                        <Badge
                          variant={project.is_active ? 'default' : 'secondary'}
                          className='text-xs'
                        >
                          {project.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        <Badge variant='outline' className='text-xs'>
                          {project.type}
                        </Badge>
                      </div>
                    </div>

                    {project.ingress_spec &&
                      project.ingress_spec.length > 0 && (
                        <div className='space-y-2'>
                          <p className='text-sm font-medium text-muted-foreground'>
                            Ingress Specifications:
                          </p>
                          <div className='grid gap-2'>
                            {project.ingress_spec.map(spec => (
                              <div
                                key={spec.id}
                                className='flex items-center justify-between bg-muted/30 p-2 rounded text-sm'
                              >
                                <div className='flex items-center gap-2'>
                                  <ExternalLink className='h-3 w-3 text-muted-foreground' />
                                  <span className='font-mono'>
                                    {spec.host}
                                    {spec.path}
                                  </span>
                                </div>
                                <Badge variant='outline' className='text-xs'>
                                  Port {spec.port}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
